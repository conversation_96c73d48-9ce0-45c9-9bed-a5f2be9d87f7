import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { auth } from "@/lib/auth";
import { getPortfolioCompositionData } from "@/utils/db/dashboard-queries";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";
import { z } from "zod";

// Validation schema for portfolio IDs and currency
const portfolioCompositionSchema = z.object({
  portfolioIds: z
    .array(z.string().uuid("ID-ul portofoliului trebuie să fie un UUID valid"))
    .min(1, "Cel puțin un portofoliu trebuie selectat"),
  displayCurrency: z.enum(["EUR", "USD", "RON"]).optional().default("EUR"),
});

// GET /api/dashboard/portfolio-composition - Get portfolio composition data
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const portfolioIdsParam = searchParams.get("portfolioIds");
    const displayCurrencyParam = searchParams.get("displayCurrency");

    if (!portfolioIdsParam) {
      return NextResponse.json(
        { error: "Parametrul portfolioIds este obligatoriu" },
        { status: 400 }
      );
    }

    // Parse portfolio IDs from comma-separated string
    let portfolioIds: string[];
    try {
      portfolioIds = portfolioIdsParam
        .split(",")
        .filter((id) => id.trim().length > 0);
    } catch (error) {
      console.error(error);
      return NextResponse.json(
        { error: "Formatul parametrului portfolioIds este invalid" },
        { status: 400 }
      );
    }

    // Validate portfolio IDs and currency
    let validatedData;
    try {
      validatedData = portfolioCompositionSchema.parse({
        portfolioIds,
        displayCurrency: displayCurrencyParam || "EUR",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: "Date invalide",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          },
          { status: 400 }
        );
      }
      throw error;
    }

    // Fetch portfolio composition data with currency conversion
    const compositionData = await getPortfolioCompositionData(
      validatedData.portfolioIds,
      validatedData.displayCurrency as SupportedCurrency
    );

    console.log(
      "GET /api/dashboard/portfolio-composition - Composition data:",
      compositionData
    );

    return NextResponse.json({
      success: true,
      data: compositionData,
      message:
        "Datele de compoziție ale portofoliului au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolio composition data:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "Nu s-au putut încărca datele de compoziție ale portofoliului" },
      { status: 500 }
    );
  }
}

// POST /api/dashboard/portfolio-composition - Alternative endpoint for large portfolio lists
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate request body
    let validatedData;
    try {
      validatedData = portfolioCompositionSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: "Date invalide",
            details: error.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          },
          { status: 400 }
        );
      }
      throw error;
    }

    // Fetch portfolio composition data with currency conversion
    const compositionData = await getPortfolioCompositionData(
      validatedData.portfolioIds,
      validatedData.displayCurrency as SupportedCurrency
    );

    console.log(
      "POST /api/dashboard/portfolio-composition - Composition data:",
      compositionData
    );

    return NextResponse.json({
      success: true,
      data: compositionData,
      message:
        "Datele de compoziție ale portofoliului au fost încărcate cu succes",
    });
  } catch (error) {
    console.error("Error fetching portfolio composition data:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "Nu s-au putut încărca datele de compoziție ale portofoliului" },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Metodă nepermisă" }, { status: 405 });
}
